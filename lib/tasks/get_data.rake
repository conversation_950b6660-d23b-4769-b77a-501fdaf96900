require 'open3'
desc "Get Screen Data"
task get_data: :environment do
  usernames = task_submit_sort
  result = {
    stats: {
      user: user_count,
      task: task_count,
      app: app_count,
      cpu: cpu_total_time,
      gpu: gpu_total_time,
      ram: ram_total_time
    },
    runTrend: {
      x: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      series: [
        cpu_usage_by_month.values,
        gpu_usage_by_month.values
      ]
    },
    employeeTask: {
      x: gpu_task_count.keys,
      series: [
        {
          name: 'GPU作业数量',
          data: gpu_task_count.values,
          color: '#5d617e'
        },
        {
          name: 'CPU作业数量',
          data: cpu_task_count.values,
          color: '#f3673b'
        }
      ]
    },
    resourceUsage: [
      { id: 'chart1', value: (((calc_nodes - free_nodes - 1) / (calc_nodes).to_f) * 100).to_i, label: '计算节点' },
      { id: 'chart2', value: ((used_cpus / (free_cpus + used_cpus).to_f) * 100).to_i, label: 'CPU' },
      { id: 'chart3', value: ((free_gpus.to_f) * 1).to_i, label: 'GPU' },
      { id: 'chart4', value: (((total_rams - free_rams) / total_rams.to_f) * 100).to_i, label: 'RAM' },
      { id: 'chart5', value: (((total_disks - free_disks) / total_disks.to_f) * 100).to_i, label: 'Disk' }
    ],
    nodeStatus: {
      running: [running_nodes],
      error: [question_nodes],
      off: [closed_nodes]
    },
    taskStatus: {
      running: running_task_count,
      queue: pendding_task_count
    },
    instanceStatus: {
      running: running_real_case_count,
      wait: pendding_real_case_count
    },
    avg: {
      calc: (task_ava_calc_time / 60 / 60.0).round(1),
      queue: (task_ava_wait_time / 60 / 60.0).round(1)
    },
    resources1: {
      y: usernames.keys[0..9],
      data: usernames.values[0..9]
    },
    resources2: {
      y: usernames.keys[10..19],
      data: usernames.values[10..19]
    }
  }

  SystemDatum.first.update(data: result.to_json)
end

# 注册用户数
def user_count
  result = `curl -G "http://************:9090/api/v1/query"   --data-urlencode "query=slurm_account_fairshare"`
  data = JSON.parse(result)
  accounts = data["data"]["result"].map { |entry| entry["metric"]["account"] }
  accounts.uniq.count
end

# 任务总计
def task_count
  # request_base('slurm_queue_completed+slurm_queue_running')
  result = `sacct --starttime=2023-01-01 --endtime=now --parsable2 --noheader --format=ReqTRES  |sed '/^$/d'  | wc -l`
  result.to_i
end

# 应用任务数
def app_count
  cmd = "ssh mgt01 'bash -l -c \"module av\"'"
  stdout, status = Open3.capture2e(cmd)
  # 使用正则表达式匹配文件/文件夹名称
  names = stdout.inspect.scan(/\b[\w.-]+\b/)

  # 排除路径信息（/usr/share/...）等，只保留文件/文件夹名
  filtered_names = names.reject { |name| name.include?('/') || name.include?('-') && stdout.include?(name + ' ') == false }
  (filtered_names - ["usr", "share", "Modules", "modulefiles", "n"]).size
end

# CPU运行时长
def cpu_total_time
  result = `sacct --allusers  --starttime 2024-01-01 --endtime now --format=CPUTime`
  times = result.scan(/\d{2}:\d{2}:\d{2}/)
  # 转换为总秒数
  total_seconds = times.map do |time_str|
    h, m, s = time_str.split(":").map(&:to_i)
    h * 3600 + m * 60 + s
  end.sum

  # 转换为小时并取整
  total_hours = (total_seconds / 3600.0).floor

  total_hours
end

# GPU运行时长
def gpu_total_time
  result = `sacct --allusers  --starttime 2024-01-01 --endtime now      --format=User,ReqTRES,Elapsed -X |        awk '{
        split($2,a,":"); gpus=a[2];
        split($3,b,/[-:]/);
        days=b[1]; h=b[2]; m=b[3]; s=b[4];
        total_seconds = (days*24*3600) + (h*3600) + (m*60) + s;
        gpu_seconds = gpus * total_seconds;
        print $1,gpus,$3,gpu_seconds/3600" GPU-hours"
      }'
  `
  times = result.scan(/\d{2}:\d{2}:\d{2}/)
  # 转换为总秒数
  total_seconds = times.map do |time_str|
    h, m, s = time_str.split(":").map(&:to_i)
    h * 3600 + m * 60 + s
  end.sum

  # 转换为小时并取整
  total_hours = (total_seconds / 3600.0).floor

  total_hours
end

# RAM运行时长
def ram_total_time
  result = `sacct --allusers --starttime 2024-01-01 --endtime now --format=ReqMem,Elapsed -X`
  times = result.scan(/\d{2}:\d{2}:\d{2}/)
  # 转换为总秒数
  total_seconds = times.map do |time_str|
    h, m, s = time_str.split(":").map(&:to_i)
    h * 3600 + m * 60 + s
  end.sum

  # 转换为小时并取整
  total_hours = (total_seconds / 3600.0).floor

  total_hours
end

# 计算节点
def calc_nodes
  request_base("count(count by (instance) (node_uname_info))")
end

def free_nodes
  request_base("slurm_nodes_idle")
end

def used_cpus
  request_base("slurm_cpus_alloc")
end

def free_cpus
  request_base("slurm_cpus_idle")
end

def total_gpus
  request_base("node_gpu_gpuDriverVersion")
end

def free_gpus
  request_base("node_gpu_utilization")
end

def total_rams
  request_base("node_memory_MemTotal_bytes")
end

def free_rams
  request_base("node_memory_MemAvailable_bytes")
end

def total_disks
  request_base("node_filesystem_size_bytes{instance='cn1:19100',mountpoint='/public'}")
end

def free_disks
  request_base("node_filesystem_avail_bytes{instance='cn1:19100',mountpoint='/public'}")
end

# 运行节点
def running_nodes
  request_base("slurm_nodes_idle+slurm_nodes_alloc+slurm_nodes_mix+slurm_nodes_comp")
end

# 故障节点
def question_nodes
  request_base("slurm_nodes_drain")
end

# 关闭节点
def closed_nodes
  request_base("slurm_nodes_down")
end

# 正在运行任务
def running_task_count
  result = `squeue -t RUNNING`
  lines = result.strip.split("\n")[1..]
  lines&.size.to_i
end

# 队列中任务
def pendding_task_count
  result = `squeue -t PENDING`
  lines = result.strip.split("\n")[1..]
  lines&.size.to_i
end

# 正在运行实例
def running_real_case_count
  result = `squeue -t RUNNING | grep sys`
  lines = result.strip.split("\n")[1..]
  lines&.size.to_i
end

# 等待分配实例
def pendding_real_case_count
  result = `squeue -t PENDING | grep sys`
  lines = result.strip.split("\n")[1..]
  lines&.size.to_i
end

# 平均计算时长
def task_ava_calc_time
  result = `sacct --starttime 2024-01-01 --endtime now  -n -X --format=Elapsed -s COMPLETED -D | awk -F: '{ if(NF==3) { total += ($1*3600) + ($2*60) + $3; count++ }
          else if(NF==2) { total += ($1*60) + $2; count++ } }
          END { if(count>0) print total/count; else print "无已完成任务" }'
          `
  result.chomp.to_i
end

# 平均排队时间
def task_ava_wait_time
  result = `sacct --format="Submit,Start" -S 2024-01-01 -E now |grep -v Unknown |grep -v None`
  data = result.split(" \n")[2..-1]
  wait_times = data.map do |line|
    submit_str, start_str = line.split
    submit_time = Time.parse(submit_str)
    start_time = Time.parse(start_str)
    start_time - submit_time
  end

  average_wait = wait_times.sum / wait_times.size
  average_wait.to_i
end

# CPU使用量
def cpu_usage_by_month
  current_year = Date.today.year
  monthly_usage = {}

  (1..12).each do |month|
    start_time = Date.new(current_year, month, 1)
    end_time = (start_time >> 1) - 1  # 下一个月的第一天减一天

    result = `sacct --allusers --starttime=#{start_time} --endtime=#{end_time} --format="ReqTRES"  --parsable2 --noheader  --state=COMPLETED |grep -v gpu |awk -F"," '{print $2}' | awk -F"=" '{print $NF}' |sed '/^$/d' | awk '{sum+=$1}END{print sum}'`

    total_count = 0
    result.split("\n").each do |tmp|
      total_count += tmp.to_i
    end

    month_key = (start_time.strftime("%m")).to_i.to_s + "月"
    monthly_usage[month_key] = total_count
  end

  monthly_usage
end

# GPU使用量
def gpu_usage_by_month
  current_year = Date.today.year
  monthly_usage = {}

  (1..12).each do |month|
    start_time = Date.new(current_year, month, 1)
    end_time = (start_time >> 1) - 1  # 下一个月的第一天减一天
    result = `sacct --allusers --starttime=#{start_time}  --endtime=#{end_time} --format="ReqTRES"  --parsable2 --noheader --state=COMPLETED |grep gpu |awk -F"," '{print $3}' | awk -F"=" '{print $NF}' | sed '/^$/d' | awk '{sum+=$1}END{print sum}'`

    total_count = 0
    result.split("\n").each do |tmp|
      total_count += tmp.to_i
    end

    month_key = (start_time.strftime("%m")).to_i.to_s + "月"
    monthly_usage[month_key] = total_count
  end

  monthly_usage
end

# GPU作业数量
def gpu_task_count
  current_year = Date.today.year
  monthly_usage = {}

  (1..12).each do |month|
    start_time = Date.new(current_year, month, 1)
    end_time = (start_time >> 1) - 1  # 下一个月的第一天减一天

    result = `sacct -a --starttime=#{start_time}  --endtime=#{end_time} --format="ReqTRES"  --parsable2 --noheader --state=COMPLETED |grep gpu  |sed '/^$/d' | wc -l`
    month_key = (start_time.strftime("%m")).to_i.to_s + "月"
    monthly_usage[month_key] = result.to_i
  end
  monthly_usage
end

# CPU作业数量
def cpu_task_count
  current_year = Date.today.year
  monthly_usage = {}

  (1..12).each do |month|
    start_time = Date.new(current_year, month, 1)
    end_time = (start_time >> 1) - 1  # 下一个月的第一天减一天

    result = `sacct -a --starttime=#{start_time}  --endtime=#{end_time} --format="ReqTRES"  --parsable2 --noheader  --state=COMPLETED  |grep -v gpu |sed '/^$/d' | wc -l`
    month_key = (start_time.strftime("%m")).to_i.to_s + "月"
    monthly_usage[month_key] = result.to_i
  end
  monthly_usage
end

# 用户任务提交排行
def task_submit_sort
  result = `sacctmgr show user`
  # 拆分为行
  lines = result.lines

  # 跳过前两行（标题和分隔符）
  user_lines = lines[2..]

  # 提取每一行的第一个字段（用户名），用正则或split
  usernames = user_lines.map do |line|
    line.strip.split(/\s+/)[0]
  end
  usernames
  user_usage = {}
  usernames.each do |username|
    data = `sacct -u #{username} --starttime=2023-01-01 --endtime=now --parsable2 --noheader --format=ReqTRES  |sed '/^$/d'  | wc -l`
    user_usage[username] = data.chomp.to_i
  end
  user_usage.sort_by { |_, v| v }.to_h
end

def request_base(type)
  begin
    result = `curl -G "http://************:9090/api/v1/query"   --data-urlencode "query=#{type}"`
    data = JSON.parse(result)
    data["data"]["result"][0]["value"][1].to_i
  rescue
    0
  end
end
