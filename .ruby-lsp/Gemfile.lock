GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.0.8)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.0.2)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.0.3)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      arel (>= 9.0)
    activestorage (*******)
      actionpack (= *******)
      activerecord (= *******)
      marcel (~> 1.0.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 0.7, < 2)
      minitest (~> 5.1)
      tzinfo (~> 1.1)
    addressable (2.8.0)
      public_suffix (>= 2.0.2, < 5.0)
    arel (9.0.0)
    autoprefixer-rails (********)
      execjs (> 0)
    bootstrap_form (4.5.0)
      actionpack (>= 5.2)
      activemodel (>= 5.2)
    browser (2.7.1)
    builder (3.2.4)
    byebug (11.1.3)
    capybara (3.35.3)
      addressable
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    childprocess (3.0.0)
    climate_control (0.2.0)
    coffee-rails (4.2.2)
      coffee-script (>= 2.2.0)
      railties (>= 4.0.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    concurrent-ruby (1.1.10)
    crass (1.0.6)
    dalli (3.2.0)
    data-confirm-modal (1.6.3)
      railties (>= 3.0)
    dotenv (2.7.6)
    dotenv-rails (2.7.6)
      dotenv (= 2.7.6)
      railties (>= 3.2)
    dotiw (5.3.3)
      activesupport
      i18n
    erubi (1.10.0)
    execjs (2.8.1)
    ffi (1.15.5)
    font-awesome-sass (5.12.0)
      sassc (>= 1.11)
    globalid (1.0.0)
      activesupport (>= 5.0)
    i18n (1.11.0)
      concurrent-ruby (~> 1.0)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jquery-datatables-rails (3.4.0)
      actionpack (>= 3.1)
      jquery-rails
      railties (>= 3.1)
      sass-rails
    jquery-rails (4.4.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    local_time (1.0.3)
      coffee-rails
    lograge (0.12.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.18.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    marcel (1.0.2)
    method_source (1.0.0)
    mini_mime (1.1.2)
    mini_portile2 (2.6.1)
    minitest (5.15.0)
    mocha (1.14.0)
    multi_json (1.15.0)
    mustermann (1.1.1)
      ruby2_keywords (~> 0.0.1)
    nio4r (2.5.8)
    nokogiri (1.12.5)
      mini_portile2 (~> 2.6.1)
      racc (~> 1.4)
    ood_appkit (1.1.5)
      addressable (~> 2.4)
      lograge (~> 0.3)
      ood_core (~> 0.1)
      rails (> 4.0.7, < 6.0)
      redcarpet (~> 3.2)
    ood_core (0.21.0)
      ffi (~> 1.9, >= 1.9.6)
      ood_support (~> 0.0.2)
      rexml (~> 3.2)
    ood_support (0.0.3)
    pbs (2.2.1)
      ffi (~> 1.9, >= 1.9.6)
    psych (4.0.4)
      stringio
    public_suffix (4.0.7)
    racc (1.6.0)
    rack (2.2.4)
    rack-protection (2.2.0)
      rack
    rack-proxy (0.7.2)
      rack
    rack-test (2.0.2)
      rack (>= 1.3)
    rails (*******)
      actioncable (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.3.0)
      railties (= *******)
      sprockets-rails (>= 2.0.0)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.4.3)
      loofah (~> 2.3)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 0.8.7)
      thor (>= 0.19.0, < 2.0)
    rake (13.0.6)
    rb-fsevent (0.11.1)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rdoc (6.4.0)
      psych (>= 4.0.0)
    redcarpet (3.5.1)
    regexp_parser (2.5.0)
    request_store (1.5.1)
      rack (>= 1.4)
    rexml (3.2.5)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    sass (3.7.4)
      sass-listen (~> 4.0.0)
    sass-listen (4.0.0)
      rb-fsevent (~> 0.9, >= 0.9.4)
      rb-inotify (~> 0.9, >= 0.9.7)
    sass-rails (5.1.0)
      railties (>= 5.2.0)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    sassc (2.4.0)
      ffi (~> 1.9)
    sdoc (2.4.0)
      rdoc (>= 5.0)
    selenium-webdriver (3.142.7)
      childprocess (>= 0.5, < 4.0)
      rubyzip (>= 1.2.2)
    semantic_range (3.0.0)
    sinatra (2.2.0)
      mustermann (~> 1.0)
      rack (~> 2.2)
      rack-protection (= 2.2.0)
      tilt (~> 2.0)
    sinatra-contrib (2.2.0)
      multi_json
      mustermann (~> 1.0)
      rack-protection (= 2.2.0)
      sinatra (= 2.2.0)
      tilt (~> 2.0)
    sprockets (3.7.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    sqlite3 (1.3.13)
    stringio (3.0.2)
    thor (0.19.1)
    thread_safe (0.3.6)
    tilt (2.0.10)
    timecop (0.9.5)
    turbolinks (5.2.1)
      turbolinks-source (~> 5.2)
    turbolinks-source (5.2.0)
    tzinfo (1.2.9)
      thread_safe (~> 0.1)
    uglifier (4.2.0)
      execjs (>= 0.3.0, < 3)
    webpacker (5.4.3)
      activesupport (>= 5.2)
      rack-proxy (>= 0.6.1)
      railties (>= 5.2)
      semantic_range (>= 2.3.0)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zip_tricks (5.6.0)

PLATFORMS
  ruby

DEPENDENCIES
  addressable (~> 2.4)
  autoprefixer-rails (~> 10.2.5)
  bootstrap_form (~> 4.0)
  browser (~> 2.2)
  byebug
  capybara (< 3.36.0)
  climate_control (~> 0.2)
  coffee-rails (~> 4.2)
  dalli (<= 3.2.0)
  data-confirm-modal (~> 1.2)
  dotenv-rails (~> 2.1)
  dotiw
  erubi
  font-awesome-sass (= 5.12.0)
  jbuilder (~> 2.0)
  jquery-datatables-rails (~> 3.4)
  jquery-rails
  local_time (~> 1.0.3)
  mocha (~> 1.1)
  nokogiri (~> 1.12.5)
  ood_appkit (~> 1.1)
  ood_core (~> 0.11)
  ood_support (~> 0.0.2)
  pbs (~> 2.2.1)
  rails (= *******)
  redcarpet (~> 3.3)
  sass-rails (~> 5.0)
  sdoc
  selenium-webdriver (< 4.0.0)
  sinatra
  sinatra-contrib
  sqlite3
  thor (= 0.19.1)
  timecop (~> 0.9)
  turbolinks (~> 5.2.0)
  uglifier (>= 1.3.0)
  webpacker (~> 5.2, >= 5.2.1)
  zip_tricks (~> 5.5)

BUNDLED WITH
   2.2.24
