<% content_for :content do %>
  <div style="display: flex;justify-content:center;font-weight: 400;font-size: 14px;">
    <div style="max-width: 1280px;">
      <div style="display: flex;gap: 20px;">
        <div>
          <div style="height: 60px;margin: 16px 0;"></div>
          <div class="left1">
            <div style="text-align:center;margin-top: -20px;">
              <div>
                <%= image_tag "/new/home/<USER>", class: "avatar" %>
              </div>
              <div>
                <%= @user.name %>
              </div>
              <div style="color: #007AFF">
                <a href="/pun/sys/dashboard/user/change_password">账号设置</a>
              </div>
              <div style="margin-top: 16px;width: 248px;text-align: left;">
                剩余机时
              </div>
              <div style="display: flex;gap: 32px;justify-content: space-around;margin-top:8px;">
                <div style="color: #007AFF;text-align:center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                  <div>3.4小时</div>
                </div>
                <div style="color: #007AFF;text-align:center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                  <div>2.4小时</div>
                </div>
                <div style="color: #007AFF;text-align:center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                  <div>1.8小时</div>
                </div>
              </div>
              <div style="display: flex;justify-content: left;flex-direction: column;">
                <div style="margin-top: 24px;width: 248px;text-align: left;">
                  账户余额
                </div>
                <div style="display: flex;align-items: baseline;">
                  <div>¥</div>
                  <div style="font-size: 20px;font-weight: 600;">152.6</div>
                </div>
              </div>


              <div style="display: flex;margin: 4px 0px">
                <div style="display: flex;align-items: center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 20px;" %></div>
                  <div style="color: #007AFF">账户记录</div>
                </div>
                <div style="display: flex;margin-left: 16px;align-items: center;">
                  <div><%= image_tag "/new/home/<USER>", style: "width: 20px;" %></div>
                  <div style="color: #007AFF">扫码充值</div>
                </div>
              </div>
              <div style="width: 248px;text-align:start;">
                优先扣除剩余机时，任有一项硬配时长不足将按计费标准扣除余额。
              </div>
            </div>
          </div>
          <div class="left2">
            <div>
              <div style="margin-top: 24px;width: 248px;text-align: left;">
                累计耗时
              </div>
              <div style="margin-top: 4px;width: 248px;text-align: left;">
                12时05分
              </div>
              <div style="margin-top: 4px;width: 248px;text-align: left;">
                成功完成28次AI训练，机时平均消耗3 分16秒
              </div>
            </div>
            <div>
              <div style="margin-top: 24px;width: 248px;text-align: left;color:#666666;">
                我的模板
              </div>
              <div style="display: flex; flex-wrap: wrap; gap: 14px;margin-top: 8px;">
                <% items = [
                  { image: "/new/home/<USER>", label: "新建模板" },
                  { image: "/new/home/<USER>", label: "自制模板" },
                  { image: "/new/home/<USER>", label: "自制模板" },
                  { image: "/new/home/<USER>", label: "自制模板" },
                  { image: "/new/home/<USER>", label: "自制模板" }
                ] %>

                <% items.each do |item| %>
                  <div style="width: calc(33.333% - 10.33px); text-align: center;">
                    <div><%= image_tag item[:image], style: "width: 40px;" %></div>
                    <div><%= item[:label] %></div>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div class="header" style="display: flex;justify-content: space-between;">
            <div><%= image_tag "/new/home/<USER>", style: "height: 60px;margin: 16px 0;" %></div>
            <div style="display: flex;align-items:center;">
              <div><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></div>
              <div><a class="logout-text" href="/logout"><%= image_tag "/new/home/<USER>", style: "height: 40px;" %></a></div>
            </div>
          </div>
          <div class="right1">
            <div class="circle-row">
              <div class="circle-col">
                <div style="padding-left: 10px;">
                  <span style="font-weight: 600;">节点</span> 总数12
                </div>
                <canvas id="circleCanvas0" width="132" height="132" style="width:132px;height:132px;display:block;"></canvas>
              </div>
              <div class="circle-col">
                <div style="padding-left: 10px;">
                  <span style="font-weight: 600;">CPU</span> 总核96
                </div>
                <canvas id="circleCanvas1" width="132" height="132" style="width:132px;height:132px;display:block;"></canvas>
              </div>
              <div class="circle-col">
                <div style="padding-left: 10px;">
                  <span style="font-weight: 600;">GPU</span> 总卡数2
                </div>
                <canvas id="circleCanvas2" width="132" height="132" style="width:132px;height:132px;display:block;"></canvas>
              </div>
              <div class="circle-col">
                <div style="padding-left: 10px;">
                  <span style="font-weight: 600;">RAM</span> 总容量16G
                </div>
                <canvas id="circleCanvas3" width="132" height="132" style="width:132px;height:132px;display:block;"></canvas>
              </div>
              <div class="circle-col">
                <div style="padding-left: 10px;">
                  <span style="font-weight: 600;">Disk</span> 总空间500G
                </div>
                <canvas id="circleCanvas4" width="132" height="132" style="width:132px;height:132px;display:block;"></canvas>
              </div>
            </div>
          </div>
          <!-- <div class="right2">
            <div style="width: 231px;height:80px;border-radius: 8px;background: white;"></div>
            <div style="width: 231px;height:80px;border-radius: 8px;background: white;"></div>
            <div style="width: 231px;height:80px;border-radius: 8px;background: white;"></div>
            <div style="width: 231px;height:80px;border-radius: 8px;background: white;"></div>
          </div> -->
          <div class="right3">
            <div style="color:#666666;">常用工具</div>
            <div style="width: 100%;height: 72px;border-radius: 16px;background: #5B93FF0D;display:flex;justify-content: space-around;">
              <div style="display: flex;align-items:center;gap:8px;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>
                  <div>脚本任务</div>
                  <div style="font-size: 12px;color:#666666;">运行.sh文件</div>
                </div>
              </div>
              <div style="display: flex;align-items:center;gap:8px;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>
                  <div>桌面环境</div>
                  <div style="font-size: 12px;color:#666666;">启动CentOS</div>
                </div>
              </div>
              <div style="display: flex;align-items:center;gap:8px;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>
                  <div>文件管理</div>
                  <div style="font-size: 12px;color:#666666;">查看文件目录</div>
                </div>
              </div>
              <div style="display: flex;align-items:center;gap:8px;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>
                  <div>启动终端</div>
                  <div style="font-size: 12px;color:#666666;">shell命令</div>
                </div>
              </div>
            </div>
            <div style="margin-top: 24px;color:#666666;">常用任务</div>
            <div style="display: flex;gap: 90px;margin-top: 8px;padding-left:25px">
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
            </div>
            <div style="margin-top: 24px;color:#666666;">脚本任务</div>
            <div style="display: flex;gap: 90px;margin-top: 8px;padding-left:25px">
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>脚本训练</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>脚本训练2</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>AI训练</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
            </div>
            <div style="margin-top: 24px;color:#666666;">实例任务</div>
            <div style="display: flex;column-gap: 90px;row-gap: 24px;margin-top: 8px;padding-left:20px;flex-wrap: wrap;">
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>脚本训练</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>脚本训练2</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>AI训练</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
              <div style="text-align: center;">
                <div><%= image_tag "/new/home/<USER>", style: "width: 38px;" %></div>
                <div>自制模板1</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- <div class="dock">
    <div class="dock-item with-divider">
      <a href="#"><%= image_tag "/new/home/<USER>" %></a>
      <span>待提交</span>
    </div>
    <div class="dock-item">
      <a href="#"><%= image_tag "/new/home/<USER>" %></a>
      <span>队列中 3</span>
    </div>
    <div class="dock-item">
      <a href="#"><%= image_tag "/new/home/<USER>" %></a>
      <span>进行中 2</span>
    </div>
    <div class="dock-item">
      <a href="#"><%= image_tag "/new/home/<USER>" %></a>
      <span>已完成 5</span>
    </div>
    <div class="dock-item">
      <a href="#"><%= image_tag "/new/home/<USER>" %></a>
      <span>已终止 4</span>
    </div>
  </div> -->
  <%#= image_tag '/new/home_bg.png' %>
  <script>
    // 每个圆环的百分比、下方两行文字和圆环颜色
    const circleData = [
      { percent: 85, lines: ['12', ' 空闲'], color: '#8373eb' },
      { percent: 90, lines: ['12', ' 空闲'], color: '#34c759' },
      { percent: 45, lines: ['80', ' 空闲'], color: '#ff9500' },
      { percent: 72, lines: ['12G', ' 空闲'], color: '#ff3b30' },
      { percent: 30, lines: ['12G', ' 空闲'], color: '#1e90ff' }
    ];

    const displaySize = 132;
    const radius = 52;
    const lineWidth = 12;
    const dpr = window.devicePixelRatio || 1;

    function drawCircle(canvas, percent, lines, color) {
      const ctx = canvas.getContext('2d');
      // 设置实际像素尺寸
      canvas.width = displaySize * dpr;
      canvas.height = displaySize * dpr;
      canvas.style.width = displaySize + 'px';
      canvas.style.height = displaySize + 'px';
      ctx.setTransform(1, 0, 0, 1, 0, 0); // 重置变换
      ctx.scale(dpr, dpr);

      const centerX = displaySize / 2;
      const centerY = displaySize / 2;

      ctx.clearRect(0, 0, displaySize, displaySize);

      // 背景圆
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      ctx.strokeStyle = '#f2f6ff';
      ctx.lineWidth = lineWidth;
      ctx.lineCap = 'round';
      ctx.stroke();

      // 进度圆弧
      ctx.beginPath();
      ctx.arc(
        centerX,
        centerY,
        radius,
        -Math.PI / 2,
        -Math.PI / 2 + (2 * Math.PI * percent) / 100,
        false
      );
      ctx.strokeStyle = color || '#8373eb';
      ctx.lineWidth = lineWidth;
      ctx.lineCap = 'round';
      ctx.stroke();

      // 百分比文本（上移，数字20px，百分号14px，底对齐）
      const percentStr = percent.toString();
      ctx.textAlign = 'center';
      ctx.textBaseline = 'alphabetic';
      // 计算总宽度
      ctx.font = 'bold 20px Arial, Helvetica, sans-serif';
      const percentNumWidth = ctx.measureText(percentStr).width;
      ctx.font = '14px Arial, Helvetica, sans-serif';
      const percentSignWidth = ctx.measureText('%').width;
      const totalWidth = percentNumWidth + percentSignWidth + 2; // 2px间距

      // 百分比数字
      // 上移 18px（原来是 centerY - 22 + 20），现在 centerY - 40 + 20
      const percentBaseY = centerY - 40 + 25; // 上移18px
      let percentNumX = centerX - totalWidth / 2 + percentNumWidth / 2;
      ctx.font = 'bold 20px Arial, Helvetica, sans-serif';
      ctx.fillStyle = '#1A1A1A';
      ctx.fillText(percentStr, percentNumX, percentBaseY);

      // 百分号
      let percentSignX = percentNumX + percentNumWidth / 2 + percentSignWidth / 2 + 2;
      ctx.font = '14px Arial, Helvetica, sans-serif';
      ctx.fillText('%', percentSignX, percentBaseY);

      // 分割线
      const lineY = centerY - 4;
      ctx.beginPath();
      ctx.moveTo(centerX - 32, lineY);
      ctx.lineTo(centerX + 32, lineY);
      ctx.strokeStyle = '#e5e5e5';
      ctx.lineWidth = 1;
      ctx.stroke();

      // 下方两行文字
      // 第一行 12px
      ctx.font = '12px Arial, Helvetica, sans-serif';
      ctx.fillStyle = '#666';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(lines[0], centerX, centerY + 12);
      // 第二行 10px
      ctx.font = '10px Arial, Helvetica, sans-serif';
      ctx.fillText(lines[1], centerX, centerY + 32 - 5);
    }

    // 渲染所有圆环
    for (let i = 0; i < 5; i++) {
      const canvas = document.getElementById('circleCanvas' + i);
      if (canvas) {
        drawCircle(canvas, circleData[i].percent, circleData[i].lines, circleData[i].color);
      }
    }
  </script>
<% end %>
<%= render 'layouts/dashboard/base' %>